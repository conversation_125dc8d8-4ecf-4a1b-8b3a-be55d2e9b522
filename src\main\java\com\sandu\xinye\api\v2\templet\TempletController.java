package com.sandu.xinye.api.v2.templet;

import com.jfinal.aop.Before;
import com.jfinal.plugin.activerecord.Record;
import com.sandu.xinye.api.v2.templet.validator.TempletGroupValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletShareValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletUpdateValidator;
import com.sandu.xinye.api.v2.templet.validator.TempletValidator;
import com.sandu.xinye.common.annotation.JsonBody;
import com.sandu.xinye.common.annotation.OperationLog;
import com.sandu.xinye.common.controller.AppController;
import com.sandu.xinye.common.interceptor.PostOnlyInterceptor;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.Templet;
import com.sandu.xinye.common.model.User;
import com.xiaoleilu.hutool.json.JSONUtil;

import java.util.List;

public class TempletController extends AppController {

    /**
     * @Title: getTempletPage
     * @Description: 获得模板分页（不查询总数）
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getTempletPage() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String name = getPara("name");
        String widthRange = getPara("width", "");
        String groupId = getPara("groupId");
        RetKit retKit = TempletService.me.getTempletPageNoCount(pageNumber, pageSize, name, getUser().getUserId(),
                groupId, widthRange);
        renderJson(retKit);
    }

    public void getTempletPageNoCount() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);
        String name = getPara("name");
        String widthRange = getPara("width", "");
        String groupId = getPara("groupId");
        RetKit retKit = TempletService.me.getTempletPageNoCount(pageNumber, pageSize, name, getUser().getUserId(),
                groupId, widthRange);
        renderJson(retKit);
    }

    /**
     * @Title: addTemplet
     * @Description: 添加模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName = "templet")
    public void addTemplet() {
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean autoRename = getParaToBoolean("autoRename", true);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // 多排标签
        Integer labelNum = getParaToInt("labelNum", 1);
        String labelGap = getPara("labelGap");
        Integer multiLabelType = getParaToInt("multiLabelType", 0);

        // labelType字段整型（标签形状） 1.矩形 2.圆角矩形 3.圆
        Integer labelType = getParaToInt("labelType", 1);
        // 模板分组
        Integer groupId = getParaToInt("groupId", -1);
        // 撕纸类型
        // 默认 1 - 撕离
        Integer paperTearType = getParaToInt("paperTearType", 1);

        RetKit ret = TempletService.me.addTemplet(groupId, name, cover, gap, height, width, printDirection, paperType,
                machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelNum, labelGap,
                multiLabelType, paperTearType, autoRename, canDuplicate, labelType);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletValidator.class)
    @OperationLog(modelName = "templet")
    public void updateTemplet() {
        String id = getPara("id");
        String name = getPara("name");
        String cover = getPara("cover");
        String gap = getPara("gap");
        Integer height = getParaToInt("height");
        Integer width = getParaToInt("width");
        Integer printDirection = getParaToInt("printDirection");
        Integer paperType = getParaToInt("paperType");
        Integer machineType = getParaToInt("machineType");
        String data = getPara("data");
        String blackLabelGap = getPara("blackLabelGap");
        String blackLabelOffset = getPara("blackLabelOffset");
        Integer cutAfterPrint = getParaToInt("cutAfterPrint", 0);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        // labelType字段整型（标签形状） 1.矩形 2.圆角矩形 3.圆
        Integer labelType = getParaToInt("labelType", 1);
        // 多排标签
        String labelGap = getPara("labelGap");
        // 多排标签
        Integer multiLabelType = getParaToInt("multiLabelType", 0);
        // 撕纸类型
        // 默认 1 - 撕离
        Integer paperTearType = getParaToInt("paperTearType", 1);
        String historyId = getPara("historyId", "");

        RetKit ret = TempletService.me.updateTemplet(id, name, cover, gap, height, width, printDirection, paperType,
                machineType, data,
                getUser().getUserId(), blackLabelGap, blackLabelOffset, cutAfterPrint, labelGap, multiLabelType,
                paperTearType, canDuplicate, labelType, historyId);
        renderJson(ret);
    }

    /**
     * @Title: updateTemplet
     * @Description: 编辑模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void copyTemplet() {
        String id = getPara("id");

        RetKit ret = TempletService.me.copyTemplet(id, getUser().getUserId());
        renderJson(ret);
    }

    /**
     * @Title: remove
     * @Description: 删除模板
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void remove() {
        String id = getPara("id");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.remove(id, userId);
        renderJson(ret);
    }

    /**
     * @Title: addGroup
     * @Description: 添加分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName = "templet_group")
    public void addGroup() {
        String name = getPara("name");
        Boolean autoRename = getParaToBoolean("autoRename", false);
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        User user = getUser();
        RetKit ret = TempletService.me.addGroup(user, name, autoRename, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: getGroupList
     * @Description: 获得分组列表
     * @date 2021年11月16日
     * <AUTHOR>
     */
    public void getGroupList() {
        RetKit ret = TempletService.me.getGroupList(getUser());
        renderJson(ret);
    }

    /**
     * @Title: moveTempletToGroup
     * @Description: 把模板移动至指定分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet_group")
    public void moveTempletToGroup() {
        String templetId = getPara("templetId");
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.moveTempletToGroup(templetId, groupId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: moveTempletsToGroup
     * @Description: 批量把模板移动至指定分组
     * @date 2024/12/6
     * <AUTHOR>
     */
    @Before({ PostOnlyInterceptor.class })
    @OperationLog(modelName = "templet_group")
    public void moveTempletsToGroup() {
        String templateIds = getPara("ids");
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.moveTempletsToGroup(templateIds, groupId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: updateTempletGroupName
     * @Description: 更新模板分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName = "templet_group")
    public void updateTempletGroupName() {
        String name = getPara("name");
        String id = getPara("id");
        Boolean canDuplicate = getParaToBoolean("duplicate", false);
        RetKit ret = TempletService.me.updateTempletGroupName(id, name, canDuplicate);
        renderJson(ret);
    }

    /**
     * @Title: deleteTempletGroup
     * @Description: 删除分组
     * @date 2021年11月16日
     * <AUTHOR>
     */
    @Before(TempletGroupValidator.class)
    @OperationLog(modelName = "templet_group")
    public void deleteTempletGroup() {
        String groupId = getPara("groupId");
        RetKit ret = TempletService.me.deleteTempletGroup(groupId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getShareSession
     * @Description: 获取模板分享session
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet_share")
    public void getShareSession() {
        long templateId = getParaToLong("templateId");
        RetKit ret = TempletService.me.getTemplateShareSession(templateId, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getBatchShareSession
     * @Description: 获取多个模板的分享session
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet_share")
    public void getBatchShareSession() {
        String templateIds = getPara("ids");
        String groupIds = getPara("groupIds");
        RetKit ret = TempletService.me.getBatchTemplateShareSession(templateIds, groupIds, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getShareSession
     * @Description: 获取模板分享session
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @Before({ PostOnlyInterceptor.class, TempletUpdateValidator.class })
    @OperationLog(modelName = "templet_share")
    public void getShareSessionByTemplateString(@JsonBody Templet templet) {
        RetKit ret = TempletService.me.getTemplateShareSessionByTemplet(templet);
        renderJson(ret);
    }

    /**
     * @Title: getTempletBySession
     * @Description: 根据session获取模板
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet_share")
    public void getTempletBySession() {
        String session = getPara("session");
        RetKit ret = TempletService.me.getTemplateByShareSession(session, getUser());
        renderJson(ret);
    }

    /**
     * @Title: getTempletByEanCode
     * @Description: 根据69码获取模板
     * @date 2021年12月23日
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet_eancode")
    public void getTempletByEanCode() {
        String eancode = getPara("eancode");
        RetKit ret = TempletService.me.getTemplateByEancode(eancode);
        renderJson(ret);
    }

    /**
     * @Description: 判断模板是否存在
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void exists() {
        String name = getPara("name");

        Boolean result = TempletService.me.isExistTemplet(name, getUser().getUserId());
        RetKit ret = RetKit.ok().set("data", result);
        renderJson(ret);
    }

    /**
     * @Description: 查询模板历史记录（前6条）
     * @date 2023年2月13日
     * <AUTHOR>
     */
    public void history() {
        Integer limit = getParaToInt("limit", 6);
        RetKit ret = TempletService.me.getTempletHistory(getUser().getUserId(), limit);
        renderJson(ret);
    }

    /**
     * @Title: shareTemplet
     * @Description: 分享模板（web端）
     * @date 2022年10月31日
     * <AUTHOR>
     */
    @Before(TempletShareValidator.class)
    public void share() {
        Record bodyPara = getArgsRecord();
        Integer templetId = bodyPara.getInt("templetId");
        String phone = bodyPara.getStr("phone");
        String shareCode = bodyPara.getStr("shareCode");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.shareTempletByPhoneAndIdentity(userId, templetId, phone, shareCode);
        renderJson(ret);
    }

    /**
     * @Title: getShareTemplet
     * @Description: 获得分享的模板（我的分享或是分享给我）
     * @date 2022年11月29日
     * <AUTHOR>
     */
    public void getShareTemplet() {
        int pageNumber = getParaToInt("pageNumber", 1);
        int pageSize = getParaToInt("pageSize", 10);

        // shareType - 1 - 我的分享 2- 分享给我
        int shareType = getParaToInt("shareType", 0);

        RetKit ret = TempletService.me.getShareTemplet(pageNumber, pageSize, getUser().getUserId(), shareType);
        renderJson(ret);
    }

    /**
     * @Title: rename
     * @Description: 重命名模板
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void rename() {
        Long id = getParaToLong("id");
        String newName = getPara("name");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.rename(id, newName, userId);
        renderJson(ret);
    }

    /**
     * @Title: batchRemove
     * @Description: 批量删除
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void batchRemove() {
        String ids = getPara("ids");
        String groupIds = getPara("groupIds");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.batchRemove(ids, groupIds, userId);
        renderJson(ret);
    }

    /**
     * @Title: batchRemove
     * @Description: 批量删除
     * @date 2024/11/25
     * <AUTHOR>
     */
    @OperationLog(modelName = "templet")
    public void removeHistory() {
        Long historyId = getParaToLong("historyId");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.removeHistory(historyId, userId);
        renderJson(ret);
    }

    /**
     * @Title: shareBySession
     * @Description: 根据session分享模板进行保存
     * @date 2024/11/26
     * <AUTHOR>
     */
    @Before({ PostOnlyInterceptor.class })
    public void shareBySession() {
        Record bodyPara = getArgsRecord();
        String session = bodyPara.getStr("session");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.saveShareTempletBySession(userId, session);
        renderJson(ret);
    }

    /**
     * @Title: batchShare
     * @Description: 多模板直接分享给伙伴
     * @date 2024/11/26
     * <AUTHOR>
     */
    @Before(PostOnlyInterceptor.class)
    public void batchShare() {
        Record bodyPara = getArgsRecord();
        String ids = bodyPara.getStr("ids");
        String groupIds = bodyPara.getStr("groupIds");
        String shareCode = bodyPara.getStr("shareCode");
        Integer userId = getUser().getUserId();
        RetKit ret = TempletService.me.batchShareTempletByIdentity(userId, ids, groupIds, shareCode);
        renderJson(ret);
    }

}
