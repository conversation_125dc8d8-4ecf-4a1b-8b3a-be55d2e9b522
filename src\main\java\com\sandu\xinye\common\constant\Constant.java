package com.sandu.xinye.common.constant;

import com.jfinal.kit.PathKit;
import com.jfinal.kit.PropKit;

public class Constant {
	// 平台后台的前端sessionId名称
	public static final String SYS_USER_SESSION_ID = "xinyeSysUserSessionId";

	// 平台app accessToken
	public static final String APP_ACCESSTOKE = "accessToken";

	/*
	 * 所有表数据 未删-0 1-已删
	 */
	public static final int IS_DEL = 1;
	public static final int NO_DEL = 0;

	public static final int QQ_STATUS = 0;
	public static final int WX_STATUS = 1;

	// 帮助类型 软件-1 硬件-2
	public static final int SOFT_HELP = 1;
	public static final int HARD_HELP = 2;

	// 处理状态 1-已处理 2-未处理
	public static final int DONE_FEEDBACK = 1;
	public static final int NOTDONE_FEEDBACK = 2;

	// 默认上传的临时文件夹绝对路径
	public static final String BASE_UPLOAD_PATH = PathKit.getWebRootPath() + "/upload/temp";
	// 相对路径
	public static final String UPLOAD_PATH = "/upload/temp";
	//真实路径
	public static final String REAL_UPLOAD_PATH = PropKit.get("uploadPath");

	public static final String REAL_UPLOAD_IP = PropKit.get("uploadIp");


	/*
	 * 发送验证码类型  1-注册  2-忘记密码 3-登录 4-修改手机号 5-注销 6-绑定手机号
	 */
	public static final String SEND_CAPTCHA_TYPE_REGISTER = "1";
	public static final String SEND_CAPTCHA_TYPE_FORGOT_PWD = "2";
	public static final String SEND_CAPTCHA_TYPE_LOGIN = "3";
	public static final String SEND_CAPTCHA_TYPE_UPDATE_PHONE = "4";
	public static final String SEND_CAPTCHA_TYPE_UNREGISTER = "5";
	public static final String SEND_CAPTCHA_TYPE_BIND_PHONE = "6";


	/**
	 * 文件类型  0-excel
	 */
	public static final int FILE_TYPE_EXCEL = 0;

	/**
	 * 模板分组数据类型  0-旧数据  1-行业模板  2-用户自定义模板分组
	 */
	public static final int TEMPLET_GROUP_TYPE_SELF_DEF_OLD = 0;
	public static final int TEMPLET_GROUP_TYPE_BUSINESS = 1;
	public static final int TEMPLET_GROUP_TYPE_SELF_DEF_NEW = 2;

	/**
	 * 模板类型  0-用户自定义模板（旧数据1.0)  1-行业模板  2-用户自定义模板(新数据）
	 */
	public static final int TEMPLET_TYPE_SELF_DEF_OLD = 0;
	public static final int TEMPLET_TYPE_BUSINESS = 1;
	public static final int TEMPLET_TYPE_SELF_DEF_NEW = 2;

	/**
	 * 打印纸张类型  1-间隙纸  2-黑标纸 3-连续纸 4-定孔纸
	 */
	public static final int PAPER_TYPE_1 = 1;
	public static final int PAPER_TYPE_2 = 2;
	public static final int PAPER_TYPE_3 = 3;
	public static final int PAPER_TYPE_4 = 4;

	/**
	 * 打印角度
	 */
	public static final int PRINT_DIRECTION_0 = 0;
	public static final int PRINT_DIRECTION_90 = 90;
	public static final int PRINT_DIRECTION_180 = 180;
	public static final int PRINT_DIRECTION_270 = 270;

	/**
	 * 默认模板名称
	 */
	public static final String TEMPLET_DEFAULT_NAME = "label";

	/**
	 * 操作日志平台类型
	 */
	public static final int OPERATION_LOG_TYPE_ADMIN = 0;
	public static final int OPERATION_LOG_TYPE_USER = 1;

	/**
	 * 国际化i18n
	 */
	public static final String LOCALE_ZH_CN = "zh_cn";
	public static final String LOCALE_EN = "en";
	public static final String LOCALE_KOREA = "korea";

	/**
	 * 分享类型
	 */
	public static final int SHARE_TYPE_FROM_ME = 1;
	public static final int SHARE_TYPE_FROM_OTHER = 2;

	/**
	 * 多排标签类型  0-不复制  1-仅复制首排标签
	 */
	public static final int TEMPLET_MUTLI_TYPE_NOCOPY = 0;
	public static final int TEMPLET_MUTLI_TYPE_COPY = 1;

	/**
	 * 短信手机号前缀
	 */
	// 台湾地区
	public static final String SMS_PREFIX_TAIWAN = "886";

	public static final int SAFE_MODE_OPEN = 1;
	public static final int SAFE_MODE_CLOSE = 0;

	/**
	 * 模板最大分享数量
	 */
	public static final int TEMPLET_SHARE_MAX = 20;

	/**
	 * 商品模板来源  0-系统内置模板  1-用户自定义模板
	 */
	public static final int GOODS_TEMPLET_TYPE_SYSTEM = 0;
	public static final int GOODS_TEMPLET_TYPE_SELF_DEF = 1;


}
